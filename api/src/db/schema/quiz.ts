import { uuid } from 'drizzle-orm/pg-core/columns/uuid';
import {
  pgTable,
  text,
  integer,
  timestamp,
  primaryKey,
} from 'drizzle-orm/pg-core';
import { relations, sql } from 'drizzle-orm';
import { questionBank } from './mcq';
import { users } from './users';
import { createInsertSchema } from 'drizzle-zod';
import { student_profiles } from './student_profile';

export enum QuizStatus {
  Inactive = 'inactive',
  Active = 'active',
  Draft = 'draft',
  Expired = 'expired',
}

export const quizSchema = pgTable('quiz', {
  id: uuid('id')
    .primaryKey()
    .default(sql`gen_random_uuid()`),
  title: text('title').notNull(),
  question_bank_id: uuid('question_bank_id')
    .notNull()
    .references(() => questionBank.id, { onDelete: 'cascade' }),
  created_by: uuid('created_by').references(() => users.id, {
    onDelete: 'set null',
  }),
  total_questions: integer('total_questions').notNull(),
  time_per_question: integer('time_per_question'),
  start_time: timestamp('start_time', { mode: 'string' }),
  end_at: timestamp('end_at', { mode: 'string' }),
  status: text('status').notNull().default(QuizStatus.Inactive),
  created_at: timestamp('created_at', { mode: 'string' })
    .notNull()
    .defaultNow(),
  updated_at: timestamp('updated_at', { mode: 'string' })
    .notNull()
    .default(sql`now()`)
    .$onUpdate(() => sql`now()`),
});

export const quizScoreSchema = pgTable(
  'quiz_score',
  {
    id: uuid('id').default(sql`gen_random_uuid()`),
    quiz_id: uuid('quiz_id')
      .notNull()
      .references(() => quizSchema.id),
    user_id: uuid('user_id')
      .notNull()
      .references(() => student_profiles.id, { onDelete: 'cascade' }),
    score: integer('score').notNull(),
    created_at: timestamp('created_at', { mode: 'string' })
      .notNull()
      .defaultNow(),
    updated_at: timestamp('updated_at', { mode: 'string' })
      .notNull()
      .default(sql`now()`)
      .$onUpdate(() => sql`now()`),
  },
  (table) => ({
    pk: primaryKey({
      name: 'quiz_score_pkey',
      columns: [table.quiz_id, table.user_id],
    }),
  }),
);

export const quizRelations = relations(quizSchema, ({ one }) => ({
  questionBank: one(questionBank, {
    fields: [quizSchema.question_bank_id],
    references: [questionBank.id],
  }),
  createdBy: one(users, {
    fields: [quizSchema.created_by],
    references: [users.id],
  }),
}));

export const quizScoreRelations = relations(quizScoreSchema, ({ one }) => ({
  quiz: one(quizSchema, {
    fields: [quizScoreSchema.quiz_id],
    references: [quizSchema.id],
  }),
  user: one(student_profiles, {
    fields: [quizScoreSchema.user_id],
    references: [student_profiles.id],
  }),
}));

export const insertQuizSchema = createInsertSchema(quizSchema).omit({
  id: true,
  created_at: true,
  updated_at: true,
});

export const insertQuizScoreSchema = createInsertSchema(quizScoreSchema).omit({
  id: true,
  created_at: true,
  updated_at: true,
});

export type Quiz = typeof quizSchema.$inferSelect;
export type QuizInput = typeof quizSchema.$inferInsert;
export type QuizScore = typeof quizScoreSchema.$inferSelect;
export const quizKeys = Object.keys(quizSchema) as [string, ...string[]];
