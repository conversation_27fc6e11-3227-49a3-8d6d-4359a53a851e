import * as Handlebars from 'handlebars';
import * as fs from 'fs';
import * as path from 'path';
import { registerHandlebarsHelpers } from '../handlebars-helpers';

describe('Bulk Deletion Report Template', () => {
  let template: HandlebarsTemplateDelegate;

  beforeAll(() => {
    // Register handlebars helpers
    registerHandlebarsHelpers();

    // Register base partial
    const baseTemplatePath = path.join(__dirname, 'base.hbs');
    if (fs.existsSync(baseTemplatePath)) {
      const baseTemplate = fs.readFileSync(baseTemplatePath, 'utf-8');
      Handlebars.registerPartial('base', baseTemplate);
    }

    // Load and compile the bulk deletion report template
    const templatePath = path.join(__dirname, 'bulk-deletion-report.hbs');
    const templateContent = fs.readFileSync(templatePath, 'utf-8');
    template = Handlebars.compile(templateContent);
  });

  it('should render template with failure details correctly', () => {
    const context = {
      adminName: 'John Admin',
      totalProcessed: 5,
      successCount: 3,
      failureCount: 2,
      failures: [
        {
          userId: 'user-1',
          email: '<EMAIL>',
          reason: 'null value in column "created_by" of relation "scheduled_notifications" violates not-null constraint'
        },
        {
          userId: 'user-2',
          email: '<EMAIL>',
          reason: 'violates foreign key constraint "fk_posts_created_by"'
        }
      ],
      completedAt: '2024-01-01 12:00:00',
      duration: '2.5 seconds'
    };

    const result = template(context);

    // Check that the template renders without errors
    expect(result).toBeDefined();
    expect(typeof result).toBe('string');

    // Check that admin name is included
    expect(result).toContain('John Admin');

    // Check that statistics are included
    expect(result).toContain('5'); // totalProcessed
    expect(result).toContain('3'); // successCount
    expect(result).toContain('2'); // failureCount

    // Check that failure details are included
    expect(result).toContain('<EMAIL>');
    expect(result).toContain('<EMAIL>');

    // Check that error messages are properly formatted
    expect(result).toContain('Database Constraint:');
    expect(result).toContain('Foreign Key Constraint:');

    // Check that the failures section is shown when there are failures
    expect(result).toContain('Failed Deletions');
    expect(result).toContain('database constraints and other issues');
  });

  it('should render template without failures section when no failures', () => {
    const context = {
      adminName: 'John Admin',
      totalProcessed: 3,
      successCount: 3,
      failureCount: 0,
      failures: [],
      completedAt: '2024-01-01 12:00:00',
      duration: '1.2 seconds'
    };

    const result = template(context);

    // Check that the template renders without errors
    expect(result).toBeDefined();
    expect(typeof result).toBe('string');

    // Check that admin name is included
    expect(result).toContain('John Admin');

    // Check that statistics are included
    expect(result).toContain('3'); // totalProcessed and successCount
    expect(result).toContain('0'); // failureCount

    // Check that failures section is not shown when there are no failures
    // The template should not contain the failures table when failureCount is 0
    expect(result).not.toContain('User Email');
    expect(result).not.toContain('Error Reason');
  });

  it('should handle different types of database errors correctly', () => {
    const context = {
      adminName: 'Admin User',
      totalProcessed: 4,
      successCount: 1,
      failureCount: 3,
      failures: [
        {
          userId: 'user-1',
          email: '<EMAIL>',
          reason: 'null value in column "created_by" of relation "scheduled_notifications" violates not-null constraint'
        },
        {
          userId: 'user-2',
          email: '<EMAIL>',
          reason: 'violates foreign key constraint "fk_posts_created_by"'
        },
        {
          userId: 'user-3',
          email: '<EMAIL>',
          reason: 'Some other database error that does not match patterns'
        }
      ],
      completedAt: '2024-01-01 12:00:00',
      duration: '3.1 seconds'
    };

    const result = template(context);

    // Check that different error types are handled correctly
    expect(result).toContain('Database Constraint:'); // First error
    expect(result).toContain('Foreign Key Constraint:'); // Second error
    expect(result).toContain('Some other database error that does not match patterns'); // Third error (raw message)

    // Check that all user emails are present
    expect(result).toContain('<EMAIL>');
    expect(result).toContain('<EMAIL>');
    expect(result).toContain('<EMAIL>');
  });
});
