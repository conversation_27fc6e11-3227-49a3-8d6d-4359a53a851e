{{#> base subject="🗑️ Bulk User Deletion Report - Operation Complete" customDisclaimer="This is an automated message from Reach. Please do not reply to this email."}}

<!-- Main Content Container -->
<tr>
  <td style="padding: 0; background-color: #ffffff;">

    <!-- Hero Header Section -->
    <table role="presentation" width="100%" cellspacing="0" cellpadding="0" border="0">
      <tr>
        <td style="background: linear-gradient(135deg, #FF6B35 0%, #FF8A65 100%); padding: 25px 20px; text-align: center; border-radius: 0;">
          <table role="presentation" width="100%" cellspacing="0" cellpadding="0" border="0">
            <tr>
              <td style="text-align: center;">
                <div style="background-color: rgba(255,255,255,0.15); border-radius: 50px; display: inline-block; padding: 8px 16px; margin-bottom: 12px;">
                  <span style="color: #ffffff; font-size: 24px;">🗑️</span>
                </div>
                <h1 style="color: #ffffff; margin: 0 0 8px 0; font-size: 24px; font-weight: bold; text-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                  Bulk User Deletion Report
                </h1>
                <p style="color: rgba(255,255,255,0.9); margin: 0 0 8px 0; font-size: 16px; font-weight: 500;">
                  Operation Status: <span style="background-color: rgba(255,255,255,0.2); padding: 4px 12px; border-radius: 20px; font-weight: bold;">COMPLETED</span>
                </p>
                <p style="color: rgba(255,255,255,0.8); margin: 0; font-size: 14px;">
                  {{completedAt}}
                </p>
              </td>
            </tr>
          </table>
        </td>
      </tr>
    </table>

    <!-- Content Wrapper -->
    <table role="presentation" width="100%" cellspacing="0" cellpadding="0" border="0">
      <tr>
        <td style="padding: 30px;">

          <!-- Greeting Section -->
          <table role="presentation" width="100%" cellspacing="0" cellpadding="0" border="0" style="margin-bottom: 20px;">
            <tr>
              <td style="background-color: #FBEAE4; padding: 18px; border-radius: 8px; border-left: 4px solid #FF6B35;">
                <p style="color: #333; margin: 0; font-size: 16px; line-height: 1.5; font-weight: 500;">
                  Hello <strong style="color: #FF6B35;">{{adminName}}</strong>,
                </p>
                <p style="color: #555; margin: 10px 0 0 0; font-size: 14px; line-height: 1.5;">
                  Your bulk user deletion operation has been completed successfully. Below is a summary of the results.
                </p>
              </td>
            </tr>
          </table>

          <!-- Statistics Dashboard -->
          <table role="presentation" width="100%" cellspacing="0" cellpadding="0" border="0" style="margin-bottom: 35px;">
            <tr>
              <td>
                <h2 style="color: #333; margin: 0 0 25px 0; font-size: 22px; font-weight: bold; text-align: center;">
                  📊 Operation Summary
                </h2>

                <!-- Statistics Cards Row -->
                <table role="presentation" width="100%" cellspacing="0" cellpadding="0" border="0">
                  <tr>
                    <!-- Total Processed Card -->
                    <td style="width: 48%; vertical-align: top; padding-right: 10px;">
                      <table role="presentation" width="100%" cellspacing="0" cellpadding="0" border="0" style="background-color: #f8f9fa; border-radius: 12px; border: 2px solid #e9ecef;">
                        <tr>
                          <td style="padding: 20px; text-align: center;">
                            <div style="background-color: #6c757d; color: white; border-radius: 50%; width: 50px; height: 50px; line-height: 50px; margin: 0 auto 15px auto; font-size: 24px; font-weight: bold;">
                              {{totalProcessed}}
                            </div>
                            <h3 style="color: #495057; margin: 0 0 5px 0; font-size: 16px; font-weight: bold;">Total Processed</h3>
                            <p style="color: #6c757d; margin: 0; font-size: 14px;">Users in operation</p>
                          </td>
                        </tr>
                      </table>
                    </td>

                    <!-- Success Rate Card -->
                    <td style="width: 48%; vertical-align: top; padding-left: 10px;">
                      <table role="presentation" width="100%" cellspacing="0" cellpadding="0" border="0" style="background-color: #d1ecf1; border-radius: 12px; border: 2px solid #bee5eb;">
                        <tr>
                          <td style="padding: 20px; text-align: center;">
                            <div style="background-color: #17a2b8; color: white; border-radius: 50%; width: 50px; height: 50px; line-height: 50px; margin: 0 auto 15px auto; font-size: 16px; font-weight: bold;">
                              ⏱️
                            </div>
                            <h3 style="color: #0c5460; margin: 0 0 5px 0; font-size: 16px; font-weight: bold;">Duration</h3>
                            <p style="color: #0c5460; margin: 0; font-size: 14px;">{{duration}}</p>
                          </td>
                        </tr>
                      </table>
                    </td>
                  </tr>
                </table>

                <!-- Success/Failure Cards Row -->
                <table role="presentation" width="100%" cellspacing="0" cellpadding="0" border="0" style="margin-top: 15px;">
                  <tr>
                    <!-- Success Card -->
                    <td style="width: 48%; vertical-align: top; padding-right: 10px;">
                      <table role="presentation" width="100%" cellspacing="0" cellpadding="0" border="0" style="background-color: #d4edda; border-radius: 12px; border: 2px solid #c3e6cb;">
                        <tr>
                          <td style="padding: 20px; text-align: center;">
                            <div style="background-color: #28a745; color: white; border-radius: 50%; width: 50px; height: 50px; line-height: 50px; margin: 0 auto 15px auto; font-size: 24px; font-weight: bold;">
                              {{successCount}}
                            </div>
                            <h3 style="color: #155724; margin: 0 0 5px 0; font-size: 16px; font-weight: bold;">✅ Successful</h3>
                            <p style="color: #155724; margin: 0; font-size: 14px;">Users deleted</p>
                          </td>
                        </tr>
                      </table>
                    </td>

                    <!-- Failure Card -->
                    <td style="width: 48%; vertical-align: top; padding-left: 10px;">
                      <table role="presentation" width="100%" cellspacing="0" cellpadding="0" border="0" style="background-color: #f8d7da; border-radius: 12px; border: 2px solid #f5c6cb;">
                        <tr>
                          <td style="padding: 20px; text-align: center;">
                            <div style="background-color: #dc3545; color: white; border-radius: 50%; width: 50px; height: 50px; line-height: 50px; margin: 0 auto 15px auto; font-size: 24px; font-weight: bold;">
                              {{failureCount}}
                            </div>
                            <h3 style="color: #721c24; margin: 0 0 5px 0; font-size: 16px; font-weight: bold;">❌ Failed</h3>
                            <p style="color: #721c24; margin: 0; font-size: 14px;">Deletion errors</p>
                          </td>
                        </tr>
                      </table>
                    </td>
                  </tr>
                </table>
              </td>
            </tr>
          </table>

          <!-- Successfully Deleted Users Section -->
          {{#if successCount}}
          <table role="presentation" width="100%" cellspacing="0" cellpadding="0" border="0" style="margin-bottom: 20px;">
            <tr>
              <td style="background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%); padding: 18px; border-radius: 8px; border-left: 4px solid #28a745;">
                <h2 style="color: #155724; margin: 0 0 6px 0; font-size: 18px; font-weight: bold;">
                  ✅ Successfully Deleted Users
                </h2>
                <p style="color: #155724; margin: 0; font-size: 14px;">
                  {{successCount}} users were successfully removed from the system
                </p>
              </td>
            </tr>
          </table>
          {{/if}}

          <!-- Failed Deletions Section -->
          {{#if failureCount}}
          <table role="presentation" width="100%" cellspacing="0" cellpadding="0" border="0" style="margin-bottom: 20px;">
            <tr>
              <td style="background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%); padding: 18px; border-radius: 8px; border-left: 4px solid #dc3545;">
                <h2 style="color: #721c24; margin: 0 0 12px 0; font-size: 18px; font-weight: bold;">
                  ❌ Failed Deletions
                </h2>
                <p style="color: #721c24; margin: 0 0 16px 0; font-size: 14px;">
                  {{failureCount}} users could not be deleted due to database constraints and other issues:
                </p>

                <!-- Failures Details Table -->
                {{#if failures}}
                <table role="presentation" width="100%" cellspacing="0" cellpadding="0" border="0" style="background-color: rgba(255,255,255,0.8); border-radius: 6px; overflow: hidden;">
                  <thead>
                    <tr style="background-color: rgba(220,53,69,0.1);">
                      <td style="padding: 10px 12px; font-size: 12px; font-weight: bold; color: #721c24; border-bottom: 1px solid rgba(220,53,69,0.2);">
                        User Email
                      </td>
                      <td style="padding: 10px 12px; font-size: 12px; font-weight: bold; color: #721c24; border-bottom: 1px solid rgba(220,53,69,0.2);">
                        Error Reason
                      </td>
                    </tr>
                  </thead>
                  <tbody>
                    {{#each failures}}
                    <tr style="border-bottom: 1px solid rgba(220,53,69,0.1);">
                      <td style="padding: 8px 12px; font-size: 13px; color: #721c24; vertical-align: top; word-break: break-word;">
                        {{email}}
                      </td>
                      <td style="padding: 8px 12px; font-size: 12px; color: #721c24; vertical-align: top; line-height: 1.4; word-break: break-word;">
                        {{#if (contains reason "violates not-null constraint")}}
                          <strong>Database Constraint:</strong> Cannot delete user due to related records that require this user reference
                        {{else if (contains reason "foreign key constraint")}}
                          <strong>Foreign Key Constraint:</strong> User has dependent records that prevent deletion
                        {{else if (contains reason "violates foreign key constraint")}}
                          <strong>Foreign Key Violation:</strong> User has related data that must be handled first
                        {{else}}
                          {{reason}}
                        {{/if}}
                      </td>
                    </tr>
                    {{/each}}
                  </tbody>
                </table>
                {{/if}}
              </td>
            </tr>
          </table>
          {{/if}}

          <!-- Critical Warning Section -->
          <table role="presentation" width="100%" cellspacing="0" cellpadding="0" border="0" style="margin-bottom: 20px;">
            <tr>
              <td style="background-color: #fff3cd; padding: 18px; border-radius: 8px; border-left: 4px solid #ffc107;">
                <h3 style="color: #856404; margin: 0 0 8px 0; font-size: 16px; font-weight: bold;">
                  ⚠️ Critical Notice: Irreversible Action
                </h3>
                <p style="color: #856404; margin: 0; font-size: 14px; line-height: 1.4;">
                  This bulk deletion operation is <strong>permanent and irreversible</strong>. All user data, profiles, and related records have been completely removed from the system and cannot be recovered.
                </p>
              </td>
            </tr>
          </table>

          <!-- Security & Compliance Notice -->
          <table role="presentation" width="100%" cellspacing="0" cellpadding="0" border="0" style="margin-bottom: 20px;">
            <tr>
              <td style="background-color: #e7f3ff; padding: 16px; border-radius: 8px; border-left: 4px solid #0066cc;">
                <p style="color: #004080; margin: 0 0 6px 0; font-size: 14px; font-weight: 600;">
                  🔒 Security & Compliance Information
                </p>
                <p style="color: #004080; margin: 0; font-size: 13px; line-height: 1.4;">
                  This deletion operation has been logged for security and compliance purposes. All actions are auditable and traceable to the administrator account that initiated the process.
                </p>
              </td>
            </tr>
          </table>

          <!-- Footer Information -->
          <table role="presentation" width="100%" cellspacing="0" cellpadding="0" border="0">
            <tr>
              <td style="padding: 16px 0; border-top: 1px solid #FBEAE4; text-align: center;">
                <p style="margin: 0 0 8px 0; color: #666; font-size: 14px; font-weight: 500;">
                  📋 Report Generated Automatically
                </p>
                <p style="margin: 0 0 6px 0; color: #888; font-size: 13px; line-height: 1.4;">
                  This report was generated automatically by the Reach system upon completion of the bulk deletion operation.
                </p>
                <p style="margin: 0; color: #888; font-size: 13px;">
                  For technical support or questions about this operation, please contact your system administrator.
                </p>
              </td>
            </tr>
          </table>

        </td>
      </tr>
    </table>

  </td>
</tr>

{{/base}}
