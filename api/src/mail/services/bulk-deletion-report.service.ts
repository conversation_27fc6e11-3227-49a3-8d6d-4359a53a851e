import { Injectable, Logger } from '@nestjs/common';
import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';
import { existsSync } from 'fs';

export interface BulkDeletionReportData {
  totalProcessed: number;
  successCount: number;
  failureCount: number;
  deletedUsers: Array<{ id: string; email: string; role: string }>;
  failures: Array<{ userId: string; email?: string; reason: string }>;
  duration: string;
  completedAt: string;
  adminName: string;
  adminEmail: string;
  adminRole: string;
}

@Injectable()
export class BulkDeletionReportService {
  private readonly logger = new Logger(BulkDeletionReportService.name);
  private readonly reportsDir = join(process.cwd(), 'storage', 'reports');

  constructor() {
    this.ensureReportsDirectory();
  }

  /**
   * Ensure the reports directory exists
   */
  private async ensureReportsDirectory(): Promise<void> {
    try {
      if (!existsSync(this.reportsDir)) {
        await mkdir(this.reportsDir, { recursive: true });
      }
    } catch (error) {
      this.logger.error('Failed to create reports directory', error);
    }
  }

  /**
   * Generate optimized template context for email rendering
   * This method optimizes the data structure for better email performance
   */
  generateOptimizedContext(data: BulkDeletionReportData): any {
    const isLargeDataset = data.successCount > 10;

    // Base context
    const context = {
      ...data,
      isLargeDataset,
      roleBreakdown: this.generateRoleBreakdown(data.deletedUsers),
    };

    // For large datasets, generate CSV and provide download link
    if (isLargeDataset) {
      const reportId = this.generateReportId();
      context.reportDownloadUrl = this.generateDownloadUrl(reportId);

      // Generate CSV file asynchronously (don't wait for it)
      this.generateCSVReport(data, reportId).catch((error) => {
        this.logger.error('Failed to generate CSV report', error);
      });

      // Limit users shown in email to first 5 for preview
      context.deletedUsers = data.deletedUsers.slice(0, 5);
    }

    return context;
  }

  /**
   * Generate role breakdown statistics
   */
  private generateRoleBreakdown(
    users: Array<{ role: string }>,
  ): Array<{ role: string; count: number }> {
    const counts = users.reduce(
      (acc, user) => {
        const role = user.role || 'unknown';
        acc[role] = (acc[role] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>,
    );

    return Object.entries(counts)
      .map(([role, count]) => ({ role, count }))
      .sort((a, b) => b.count - a.count); // Sort by count descending
  }

  /**
   * Generate a unique report ID
   */
  private generateReportId(): string {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const random = Math.random().toString(36).substring(2, 8);
    return `bulk-deletion-${timestamp}-${random}`;
  }

  /**
   * Generate download URL for the report
   */
  private generateDownloadUrl(reportId: string): string {
    const baseUrl = process.env.FRONTEND_URL || 'http://localhost:3000';
    return `${baseUrl}/api/reports/download/${reportId}`;
  }

  /**
   * Generate CSV report for large datasets
   */
  private async generateCSVReport(
    data: BulkDeletionReportData,
    reportId: string,
  ): Promise<void> {
    try {
      const csvContent = this.generateCSVContent(data);
      const filePath = join(this.reportsDir, `${reportId}.csv`);

      await this.ensureReportsDirectory();
      await writeFile(filePath, csvContent, 'utf-8');

      this.logger.log(`CSV report generated: ${reportId}.csv`);
    } catch (error) {
      this.logger.error('Failed to generate CSV report', error);
      throw error;
    }
  }

  /**
   * Generate CSV content from deletion data
   */
  private generateCSVContent(data: BulkDeletionReportData): string {
    const headers = [
      'Type',
      'Email',
      'Role',
      'Status',
      'Reason',
      'Processed At',
    ];
    const rows = [headers.join(',')];

    // Add successful deletions
    data.deletedUsers.forEach((user) => {
      const row = [
        'Success',
        `"${user.email}"`,
        `"${user.role}"`,
        'Deleted',
        '',
        `"${data.completedAt}"`,
      ];
      rows.push(row.join(','));
    });

    // Add failed deletions
    data.failures.forEach((failure) => {
      const row = [
        'Failure',
        `"${failure.email || 'Unknown'}"`,
        '',
        'Failed',
        `"${failure.reason}"`,
        `"${data.completedAt}"`,
      ];
      rows.push(row.join(','));
    });

    // Add summary row
    rows.push('');
    rows.push('Summary');
    rows.push(`Total Processed,${data.totalProcessed}`);
    rows.push(`Successful,${data.successCount}`);
    rows.push(`Failed,${data.failureCount}`);
    rows.push(`Duration,${data.duration}`);
    rows.push(`Admin,${data.adminEmail}`);
    rows.push(`Completed At,${data.completedAt}`);

    return rows.join('\n');
  }

  /**
   * Calculate email size estimation
   */
  calculateEmailSize(data: BulkDeletionReportData): {
    estimatedSize: number;
    isOptimal: boolean;
    recommendation: string;
  } {
    // Base template size (approximately)
    const baseSize = 15000; // 15KB for headers, styling, etc.

    // Estimate size per user row (approximately)
    const sizePerUser = 200; // 200 bytes per user row
    const sizePerFailure = 300; // 300 bytes per failure row (includes reason)

    const userDataSize =
      data.successCount * sizePerUser + data.failureCount * sizePerFailure;
    const estimatedSize = baseSize + userDataSize;

    // Email size recommendations
    const isOptimal = estimatedSize < 50000; // 50KB
    const isAcceptable = estimatedSize < 100000; // 100KB

    let recommendation: string;
    if (isOptimal) {
      recommendation = 'Email size is optimal for all clients';
    } else if (isAcceptable) {
      recommendation =
        'Email size is acceptable but consider optimization for mobile clients';
    } else {
      recommendation =
        'Email size is too large - use summary view with CSV download';
    }

    return {
      estimatedSize,
      isOptimal,
      recommendation,
    };
  }

  /**
   * Get performance metrics for template rendering
   */
  getPerformanceMetrics(data: BulkDeletionReportData): {
    renderingComplexity: 'low' | 'medium' | 'high';
    recommendedApproach: 'detailed' | 'summary' | 'csv-only';
    estimatedRenderTime: number;
  } {
    const totalItems = data.successCount + data.failureCount;

    let renderingComplexity: 'low' | 'medium' | 'high';
    let recommendedApproach: 'detailed' | 'summary' | 'csv-only';
    let estimatedRenderTime: number;

    if (totalItems <= 10) {
      renderingComplexity = 'low';
      recommendedApproach = 'detailed';
      estimatedRenderTime = 100; // 100ms
    } else if (totalItems <= 50) {
      renderingComplexity = 'medium';
      recommendedApproach = 'summary';
      estimatedRenderTime = 500; // 500ms
    } else {
      renderingComplexity = 'high';
      recommendedApproach = 'csv-only';
      estimatedRenderTime = 1000; // 1s+
    }

    return {
      renderingComplexity,
      recommendedApproach,
      estimatedRenderTime,
    };
  }

  /**
   * Clean up old report files (call this periodically)
   */
  async cleanupOldReports(maxAgeHours: number = 24): Promise<void> {
    try {
      const { readdir, stat, unlink } = await import('fs/promises');
      const files = await readdir(this.reportsDir);
      const now = Date.now();
      const maxAge = maxAgeHours * 60 * 60 * 1000;

      for (const file of files) {
        if (file.endsWith('.csv')) {
          const filePath = join(this.reportsDir, file);
          const stats = await stat(filePath);

          if (now - stats.mtime.getTime() > maxAge) {
            await unlink(filePath);
            this.logger.log(`Cleaned up old report: ${file}`);
          }
        }
      }
    } catch (error) {
      this.logger.error('Failed to cleanup old reports', error);
    }
  }
}
