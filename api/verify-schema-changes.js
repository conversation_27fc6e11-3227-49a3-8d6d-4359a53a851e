const { Pool } = require('pg');
require('dotenv').config();

// Parse DATABASE_URL
const databaseUrl = process.env.DATABASE_URL;
if (!databaseUrl) {
  throw new Error('DATABASE_URL environment variable is required');
}

// Database connection configuration
const pool = new Pool({
  connectionString: databaseUrl,
  ssl: { rejectUnauthorized: false }, // Required for AWS RDS
});

async function verifySchemaChanges() {
  console.log('🔍 Verifying Database Schema Changes');
  console.log('====================================');

  try {
    // Test 1: Check if scheduled_notifications.created_by is nullable
    console.log(
      '\n1. Checking scheduled_notifications.created_by nullability...',
    );
    const nullabilityQuery = `
      SELECT column_name, is_nullable, data_type
      FROM information_schema.columns
      WHERE table_name = 'scheduled_notifications'
      AND column_name = 'created_by';
    `;

    const nullabilityResult = await pool.query(nullabilityQuery);
    if (nullabilityResult.rows.length > 0) {
      const column = nullabilityResult.rows[0];
      console.log(`   Column: ${column.column_name}`);
      console.log(`   Type: ${column.data_type}`);
      console.log(`   Nullable: ${column.is_nullable}`);

      if (column.is_nullable === 'YES') {
        console.log('   ✅ PASS: created_by column is nullable');
      } else {
        console.log('   ❌ FAIL: created_by column is still NOT NULL');
      }
    } else {
      console.log(
        '   ❌ FAIL: scheduled_notifications.created_by column not found',
      );
    }

    // Test 2: Check foreign key constraints
    console.log('\n2. Checking foreign key constraints...');
    const constraintsQuery = `
      SELECT
          tc.constraint_name,
          tc.table_name,
          kcu.column_name,
          ccu.table_name AS foreign_table_name,
          ccu.column_name AS foreign_column_name,
          rc.delete_rule
      FROM information_schema.table_constraints AS tc
      JOIN information_schema.key_column_usage AS kcu
          ON tc.constraint_name = kcu.constraint_name
      JOIN information_schema.constraint_column_usage AS ccu
          ON ccu.constraint_name = tc.constraint_name
      JOIN information_schema.referential_constraints AS rc
          ON tc.constraint_name = rc.constraint_name
      WHERE tc.constraint_type = 'FOREIGN KEY'
      AND ccu.table_name = 'users'
      ORDER BY tc.table_name, tc.constraint_name;
    `;

    const constraintsResult = await pool.query(constraintsQuery);
    console.log('   Foreign key constraints referencing users table:');

    const expectedConstraints = {
      scheduled_notifications_created_by_users_id_fk: 'SET NULL',
      questions_created_by_users_id_fk: 'SET NULL',
      points_logs_student_id_student_profiles_id_fk: 'CASCADE', // This references student_profiles, not users directly
    };

    let allConstraintsCorrect = true;

    constraintsResult.rows.forEach((row) => {
      console.log(
        `   - ${row.table_name}.${row.column_name} -> ${row.foreign_table_name}.${row.foreign_column_name}`,
      );
      console.log(`     Constraint: ${row.constraint_name}`);
      console.log(`     Delete Rule: ${row.delete_rule}`);

      if (expectedConstraints[row.constraint_name]) {
        if (
          row.delete_rule.toUpperCase() ===
          expectedConstraints[row.constraint_name]
        ) {
          console.log(`     ✅ PASS: Correct delete rule`);
        } else {
          console.log(
            `     ❌ FAIL: Expected ${expectedConstraints[row.constraint_name]}, got ${row.delete_rule}`,
          );
          allConstraintsCorrect = false;
        }
      }
      console.log('');
    });

    // Test 3: Test actual user deletion (if we have test data)
    console.log('\n3. Testing user deletion functionality...');
    console.log(
      '   Note: This would require creating test data and attempting deletion',
    );
    console.log(
      '   For safety, this test is skipped in this verification script',
    );

    // Summary
    console.log('\n📋 Summary');
    console.log('==========');

    const scheduledNotificationsNullable =
      nullabilityResult.rows[0]?.is_nullable === 'YES';

    if (scheduledNotificationsNullable && allConstraintsCorrect) {
      console.log('✅ All schema changes appear to be applied correctly!');
      console.log(
        '✅ User deletion should now work without foreign key constraint violations',
      );
    } else {
      console.log('❌ Some schema changes may not have been applied correctly');
      console.log(
        '❌ User deletion may still encounter foreign key constraint violations',
      );
    }

    console.log('\n🧪 Next Steps for Testing:');
    console.log('1. Create a test user with some related data');
    console.log(
      '2. Create scheduled notifications with that user as created_by',
    );
    console.log('3. Attempt to delete the user via the API');
    console.log('4. Verify that:');
    console.log('   - User is deleted successfully');
    console.log('   - scheduled_notifications.created_by is set to NULL');
    console.log('   - Related data is handled according to constraints');
  } catch (error) {
    console.error('❌ Error verifying schema changes:', error.message);
    throw error;
  } finally {
    await pool.end();
  }
}

// Run the verification
if (require.main === module) {
  verifySchemaChanges()
    .then(() => {
      console.log('\n✅ Schema verification completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Schema verification failed:', error);
      process.exit(1);
    });
}

module.exports = { verifySchemaChanges };
