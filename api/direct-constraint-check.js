const { Pool } = require('pg');
require('dotenv').config();

const databaseUrl = process.env.DATABASE_URL;
const pool = new Pool({
  connectionString: databaseUrl,
  ssl: { rejectUnauthorized: false },
});

async function directConstraintCheck() {
  try {
    const pgConstraintQuery = \`
      SELECT 
          con.conname AS constraint_name,
          rel.relname AS table_name,
          att.attname AS column_name,
          frel.relname AS foreign_table_name,
          fatt.attname AS foreign_column_name,
          CASE con.confdeltype
              WHEN 'a' THEN 'NO ACTION'
              WHEN 'r' THEN 'RESTRICT'
              WHEN 'c' THEN 'CASCADE'
              WHEN 'n' THEN 'SET NULL'
              WHEN 'd' THEN 'SET DEFAULT'
              ELSE con.confdeltype::text
          END AS delete_rule
      FROM pg_constraint con
      JOIN pg_class rel ON con.conrelid = rel.oid
      JOIN pg_namespace nsp ON rel.relnamespace = nsp.oid
      JOIN pg_attribute att ON att.attrelid = con.conrelid AND att.attnum = con.conkey[1]
      JOIN pg_class frel ON con.confrelid = frel.oid
      JOIN pg_attribute fatt ON fatt.attrelid = con.confrelid AND fatt.attnum = con.confkey[1]
      WHERE con.contype = 'f'
      AND nsp.nspname = 'public'
      ORDER BY rel.relname, con.conname;
    \`;

    const result = await pool.query(pgConstraintQuery);
    
    console.log('🔍 Foreign Key Constraints Found:', result.rows.length);
    
    const quizConstraints = result.rows.filter(row => 
      row.table_name === 'quiz_score' || row.constraint_name.includes('quiz')
    );
    
    console.log('\\n📋 Quiz-related constraints:');
    quizConstraints.forEach(row => {
      console.log(\`- \${row.table_name}.\${row.column_name} -> \${row.foreign_table_name}.\${row.foreign_column_name}\`);
      console.log(\`  Constraint: \${row.constraint_name}\`);
      console.log(\`  Delete Rule: \${row.delete_rule}\`);
      console.log('');
    });

    const quizIdConstraint = result.rows.find(row => row.constraint_name === 'quiz_score_quiz_id_quiz_id_fk');
    if (quizIdConstraint) {
      console.log(\`✅ Found quiz_score.quiz_id constraint: \${quizIdConstraint.delete_rule}\`);
      if (quizIdConstraint.delete_rule === 'CASCADE') {
        console.log('✅ Quiz deletion should work with CASCADE');
      } else {
        console.log(\`❌ Quiz deletion blocked by \${quizIdConstraint.delete_rule} constraint\`);
      }
    } else {
      console.log('❌ quiz_score_quiz_id_quiz_id_fk constraint not found');
    }

  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    await pool.end();
  }
}

directConstraintCheck();
