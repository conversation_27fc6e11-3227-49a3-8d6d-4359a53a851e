-- Fix foreign key constraints and nullable columns for proper user deletion
-- This migration addresses several constraint issues that prevent clean user deletion

-- 1. Make the created_by column nullable in scheduled_notifications table
-- This allows the SET NULL foreign key constraint to work properly when users are deleted
ALTER TABLE "scheduled_notifications" ALTER COLUMN "created_by" DROP NOT NULL;

-- 2. Fix questions.created_by constraint from RESTRICT to SET NULL
-- The schema expects SET NULL but the database has RESTRICT
ALTER TABLE "questions" DROP CONSTRAINT "questions_created_by_users_id_fk";
--> statement-breakpoint
ALTER TABLE "questions" ADD CONSTRAINT "questions_created_by_users_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."users"("id") ON DELETE set null ON UPDATE no action;

-- 3. Fix points_logs constraints to CASCADE properly when student_profiles are deleted
-- This ensures points_logs are cleaned up when users (and their student_profiles) are deleted
ALTER TABLE "points_logs" DROP CONSTRAINT "points_logs_student_id_student_profiles_id_fk";
--> statement-breakpoint
ALTER TABLE "points_logs" ADD CONSTRAINT "points_logs_student_id_student_profiles_id_fk" FOREIGN KEY ("student_id") REFERENCES "public"."student_profiles"("id") ON DELETE cascade ON UPDATE no action;
