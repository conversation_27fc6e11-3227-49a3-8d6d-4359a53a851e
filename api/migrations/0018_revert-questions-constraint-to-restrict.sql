-- Revert questions.created_by foreign key constraint from SET NULL back to RESTRICT
-- This allows for alternative handling approaches for questions when users are deleted

-- Revert questions.created_by constraint from SET NULL back to RESTRICT
-- This will prevent user deletion when they have created questions, requiring explicit handling
ALTER TABLE "questions" DROP CONSTRAINT "questions_created_by_users_id_fk";
--> statement-breakpoint
ALTER TABLE "questions" ADD CONSTRAINT "questions_created_by_users_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."users"("id") ON DELETE restrict ON UPDATE no action;
