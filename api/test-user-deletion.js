const axios = require('axios');

const API_BASE_URL = 'http://localhost:8000/api/v1';

// Test configuration
const TEST_CONFIG = {
  // Use a test email that won't conflict with existing users
  testEmail: `test-deletion-${Date.now()}@example.com`,
  adminEmail: '<EMAIL>', // Assuming this is a super_admin
  baseUrl: API_BASE_URL,
};

let testUserId = null;
let adminToken = null;

async function makeRequest(method, endpoint, data = null, token = null) {
  try {
    const config = {
      method,
      url: `${TEST_CONFIG.baseUrl}${endpoint}`,
      headers: {
        'Content-Type': 'application/json',
        'x-client-type': 'web',
        ...(token && { Authorization: `Bearer ${token}` }),
      },
      ...(data && { data }),
    };

    const response = await axios(config);
    return { success: true, data: response.data, status: response.status };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data || error.message,
      status: error.response?.status,
    };
  }
}

async function loginAsAdmin() {
  console.log('🔐 Logging in as admin...');

  const loginResult = await makeRequest('POST', '/auth/login', {
    email: TEST_CONFIG.adminEmail,
  });

  if (!loginResult.success) {
    throw new Error(
      `Failed to initiate admin login: ${JSON.stringify(loginResult.error)}`,
    );
  }

  console.log(
    '✅ Admin login initiated. Please check for OTP and enter it manually for testing.',
  );
  console.log(
    'Note: For automated testing, you would need to implement OTP verification.',
  );

  // For now, we'll assume the admin is already logged in or use a test token
  // In a real test, you'd need to handle OTP verification
  return null; // Return null to indicate manual intervention needed
}

async function createTestUser() {
  console.log('👤 Creating test user...');

  const createResult = await makeRequest('POST', '/auth/login', {
    email: TEST_CONFIG.testEmail,
  });

  if (!createResult.success) {
    throw new Error(
      `Failed to create test user: ${JSON.stringify(createResult.error)}`,
    );
  }

  console.log('✅ Test user creation initiated');
  return createResult.data;
}

async function testUserDeletion() {
  console.log('🧪 Starting User Deletion Test');
  console.log('================================');

  try {
    // Step 1: Login as admin (manual step for now)
    console.log('\n📋 Test Steps:');
    console.log('1. Login as admin manually and get token');
    console.log('2. Create a test user');
    console.log('3. Create some related data (scheduled notifications, etc.)');
    console.log('4. Attempt to delete the user');
    console.log('5. Verify deletion was successful');

    // For manual testing, we'll create a test user first
    await createTestUser();

    console.log('\n⚠️  Manual Testing Required:');
    console.log('1. Login as admin and get your JWT token');
    console.log('2. Create a test user and note their ID');
    console.log(
      '3. Create some scheduled notifications with that user as created_by',
    );
    console.log('4. Try to delete the user using the admin endpoint');
    console.log(
      '5. Check if the deletion succeeds without foreign key constraint errors',
    );

    console.log('\n🔧 Test Endpoints:');
    console.log(`POST ${TEST_CONFIG.baseUrl}/auth/login`);
    console.log(`DELETE ${TEST_CONFIG.baseUrl}/auth/user/{userId}`);
    console.log(`DELETE ${TEST_CONFIG.baseUrl}/auth/delete-account`);

    console.log('\n📝 Expected Behavior:');
    console.log(
      '- User deletion should succeed without foreign key constraint violations',
    );
    console.log('- scheduled_notifications.created_by should be set to NULL');
    console.log('- student_profiles should be deleted (CASCADE)');
    console.log(
      '- points_logs should be deleted (CASCADE via student_profiles)',
    );
    console.log(
      '- Other related data should be handled according to their constraints',
    );
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    throw error;
  }
}

// Simple test to verify the migration was applied
async function testDatabaseSchema() {
  console.log('\n🗄️  Testing Database Schema Changes...');

  // This would require a direct database connection to verify
  // For now, we'll just document what should be checked
  console.log('📋 Schema Verification Checklist:');
  console.log('1. scheduled_notifications.created_by should be nullable');
  console.log('2. Foreign key constraint should be SET NULL on user deletion');
  console.log(
    '3. questions.created_by constraint should be SET NULL (not RESTRICT)',
  );
  console.log('4. points_logs.student_id constraint should be CASCADE');

  console.log('\n🔍 Manual Verification SQL:');
  console.log(`
-- Check if created_by is nullable
SELECT column_name, is_nullable, data_type
FROM information_schema.columns
WHERE table_name = 'scheduled_notifications'
AND column_name = 'created_by';

-- Check foreign key constraints
SELECT
    tc.constraint_name,
    tc.table_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name,
    rc.delete_rule
FROM information_schema.table_constraints AS tc
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
JOIN information_schema.referential_constraints AS rc
    ON tc.constraint_name = rc.constraint_name
WHERE tc.constraint_type = 'FOREIGN KEY'
AND ccu.table_name = 'users'
ORDER BY tc.table_name, tc.constraint_name;
  `);
}

// Run the tests
if (require.main === module) {
  testUserDeletion()
    .then(() => testDatabaseSchema())
    .then(() => {
      console.log(
        '\n✅ Test completed. Please verify manually using the provided endpoints and SQL queries.',
      );
    })
    .catch((error) => {
      console.error('\n❌ Test failed:', error);
      process.exit(1);
    });
}

module.exports = {
  testUserDeletion,
  testDatabaseSchema,
  TEST_CONFIG,
};
