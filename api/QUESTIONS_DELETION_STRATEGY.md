# Questions Deletion Strategy

## Overview
This document outlines the approach for handling questions when users are deleted. Unlike other entities that use CASCADE or SET NULL constraints, questions require special handling due to their importance and potential impact on the quiz system.

## Current Implementation
- **Foreign Key Constraint**: `questions.created_by` → `users.id` with `ON DELETE RESTRICT`
- **Behavior**: User deletion is **blocked** if they have created any questions
- **Rationale**: Questions are valuable content that should be preserved and require explicit handling

## Why RESTRICT Instead of SET NULL or CASCADE?

### Problems with SET NULL
- **Data Integrity**: Questions without a creator become "orphaned" and lose important audit trail
- **Attribution Loss**: No way to track who created valuable quiz content
- **Quality Control**: Difficult to identify and manage questions from problematic users

### Problems with CASCADE
- **Data Loss**: Deleting a user would remove all their questions, potentially destroying valuable quiz content
- **System Impact**: Could break existing quizzes that reference those questions
- **Irreversible**: No way to recover accidentally deleted questions

### Benefits of RESTRICT
- **Data Preservation**: Ensures valuable quiz content is never accidentally lost
- **Explicit Handling**: Forces administrators to make conscious decisions about question ownership
- **Audit Trail**: Maintains clear ownership and accountability for content

## Alternative Handling Approaches

### 1. **System User Reassignment** (Recommended)
Create a dedicated system user account and reassign questions to it before user deletion.

```typescript
// Implementation approach
async function reassignQuestionsToSystemUser(userId: string) {
  const systemUser = await this.getOrCreateSystemUser();
  
  await this.db
    .update(questions)
    .set({ created_by: systemUser.id })
    .where(eq(questions.created_by, userId));
}

async function deleteUserWithQuestionReassignment(userId: string) {
  // 1. Reassign questions to system user
  await this.reassignQuestionsToSystemUser(userId);
  
  // 2. Now user can be deleted safely
  await this.deleteUser(userId);
}
```

**Pros:**
- Preserves all question content
- Maintains database integrity
- Clear audit trail (questions marked as system-owned)
- Automated and scalable

**Cons:**
- Requires system user management
- Questions lose original creator attribution

### 2. **Admin Review and Manual Reassignment**
Require administrator review before user deletion when questions exist.

```typescript
async function checkUserDeletionEligibility(userId: string) {
  const questionCount = await this.db
    .select({ count: count() })
    .from(questions)
    .where(eq(questions.created_by, userId));
    
  if (questionCount[0].count > 0) {
    throw new ConflictException(
      `Cannot delete user: ${questionCount[0].count} questions require reassignment. ` +
      `Please reassign questions to another user first.`
    );
  }
}
```

**Pros:**
- Human oversight ensures quality decisions
- Can reassign to appropriate subject matter experts
- Maintains original attribution if desired

**Cons:**
- Manual process doesn't scale
- Requires administrator intervention
- Delays user deletion process

### 3. **Question Archive and Soft Delete**
Archive questions instead of reassigning them.

```typescript
// Add archived status to questions
async function archiveUserQuestions(userId: string) {
  await this.db
    .update(questions)
    .set({ 
      is_archived: true,
      archived_reason: 'User deletion',
      archived_at: new Date()
    })
    .where(eq(questions.created_by, userId));
}
```

**Pros:**
- Preserves original attribution
- Questions can be reviewed later
- Clear audit trail of why questions were archived

**Cons:**
- Archived questions may still appear in some queries
- Requires additional schema changes
- May complicate quiz generation logic

### 4. **Bulk Question Transfer**
Transfer all questions to a designated administrator or content manager.

```typescript
async function transferQuestionsToAdmin(userId: string, adminUserId: string) {
  await this.db
    .update(questions)
    .set({ 
      created_by: adminUserId,
      transfer_notes: `Transferred from deleted user ${userId}`,
      transferred_at: new Date()
    })
    .where(eq(questions.created_by, userId));
}
```

**Pros:**
- Questions remain active and usable
- Clear ownership transfer
- Can include transfer metadata

**Cons:**
- Requires identifying appropriate admin
- May overwhelm admin with questions they didn't create
- Requires additional schema fields for transfer tracking

## Recommended Implementation

### Phase 1: System User Approach
Implement the **System User Reassignment** approach as it provides the best balance of automation, data preservation, and simplicity.

1. **Create System User**: Create a dedicated "System" user account for orphaned content
2. **Update User Deletion Service**: Modify the user deletion process to reassign questions first
3. **Add Logging**: Log all question reassignments for audit purposes

### Phase 2: Enhanced Handling (Future)
Consider implementing additional features:
- **Question Review Dashboard**: Allow admins to review and reassign system-owned questions
- **Bulk Transfer Tools**: Provide UI for transferring questions between users
- **Question Attribution History**: Track the full ownership history of questions

## Implementation Example

```typescript
@Injectable()
export class UserDeletionService {
  private readonly SYSTEM_USER_EMAIL = '<EMAIL>';
  
  async deleteUser(userId: string): Promise<void> {
    // Check if user has questions
    const questionCount = await this.getQuestionCount(userId);
    
    if (questionCount > 0) {
      // Reassign questions to system user
      await this.reassignQuestionsToSystemUser(userId);
      
      // Log the reassignment
      this.logger.log(
        `Reassigned ${questionCount} questions from user ${userId} to system user`
      );
    }
    
    // Proceed with normal user deletion
    await this.performUserDeletion(userId);
  }
  
  private async reassignQuestionsToSystemUser(userId: string): Promise<void> {
    const systemUser = await this.getOrCreateSystemUser();
    
    const result = await this.db
      .update(questions)
      .set({ 
        created_by: systemUser.id,
        updated_at: new Date()
      })
      .where(eq(questions.created_by, userId));
      
    return result;
  }
  
  private async getOrCreateSystemUser(): Promise<User> {
    let systemUser = await this.userRepository.findByEmail(this.SYSTEM_USER_EMAIL);
    
    if (!systemUser) {
      systemUser = await this.userRepository.create({
        email: this.SYSTEM_USER_EMAIL,
        name: 'System User',
        role: 'system',
        state: 'active'
      });
    }
    
    return systemUser;
  }
}
```

## Testing Strategy

1. **Unit Tests**: Test question reassignment logic
2. **Integration Tests**: Test full user deletion flow with questions
3. **Edge Cases**: Test with large numbers of questions, system user creation, etc.
4. **Performance Tests**: Ensure reassignment doesn't impact deletion performance

## Migration Path

1. **Apply RESTRICT constraint** (✅ Completed)
2. **Implement system user approach** (Next step)
3. **Update user deletion endpoints** to handle questions
4. **Add monitoring and logging** for question reassignments
5. **Create admin tools** for question management (future enhancement)

This approach ensures that valuable quiz content is never lost while providing a clear, automated path for user deletion.
